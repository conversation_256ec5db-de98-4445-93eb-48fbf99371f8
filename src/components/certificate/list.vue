<template>
<el-dialog
    :visible.sync="dialogTarnShow"
    title="查看凭证"
    width="65%"
    :before-close="closeTarnShow"
  >
    <resultTrans v-for="(item,idx) in showTableTrans" :key="idx" :tableData="item" :classList="kjClassList"></resultTrans>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogTarnShow = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script>
import resultTrans from '@/views/system/businessPayment/resultTrans.vue';
import {getAcctgTransListByCode, getHaokjSub,} from '@/api/business/processapi'
export default {
  name: 'CertificateList',
  components: {resultTrans},
  computed: {
  },
  data() {
    return {
      dialogTarnShow: false,
      showTableTrans: [],
      kjClassList: [],
    }
  },
  created() {
    this.loadKeMuList()
  },
  methods: {
    open(id,code) {
      this.showTranByCode(id, code)
    },
    loadKeMuList() {
      getHaokjSub().then(res => {
        if (res && res.data) {
          this.kjClassList = res.data || []
        }
      })
    },
     showTranByCode(id,code) {
      this.loadTranListByProcessIdAndCode(id, code, () => {
        this.dialogTarnShow = true
      })
    },
    loadTranListByProcessIdAndCode(pid, code,func=undefined) {
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.2)'
      })
      this.showTableTrans = []
      getAcctgTransListByCode(pid, code).then(res => {
        this.showTableTrans = res.result
        // biome-ignore lint/complexity/useOptionalChain: <explanation>
        func && func()
      })
      .catch(() => {
        this.$message.error('加载失败')
      })
      .finally(() => {
        loading.close()
      })
    },
    closeTarnShow() {
      this.dialogTarnShow = false
    },
  }
}
</script>
<style scoped></style>
