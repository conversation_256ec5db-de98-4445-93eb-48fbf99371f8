import request from '@/utils/request'
const BASE_REQUEST = '/api/sysProcessDetail'

export function add(data) {
  return request({
    url: 'api/sysProcessDetail',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/sysProcessDetail/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/sysProcessDetail',
    method: 'put',
    data
  })
}

export function getProcessCodeList() {
  return request({
    url: `${BASE_REQUEST}/getProcessCodeList`,
    method: 'get'
  })
}

export function detailByCode(code) {
  return request({
    url: 'api/sysProcessDetail/detailByCode',
    method: 'get',
    params: {
      spare1: code
    }
  })
}

export default { add, edit, del }
